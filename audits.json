{"isGameApp": false, "score": 77, "level": "B", "tasks": [{"meta": {"id": "deprecated-api", "passedTitle": "未发现使用废弃接口", "failedTitle": "发现正在使用废弃接口", "description": "使用即将废弃或已废弃接口，可能导致小程序运行不正常。一般而言，接口不会立即去掉，但保险起见，建议不要使用，避免后续小程序突然运行异常。", "document": ""}, "weight": 2, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "passed", "title": "未发现使用废弃接口", "headings": [{"key": "api", "text": "组件/API"}, {"key": "stack", "text": "调用栈", "type": "stack"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "dom-size", "passedTitle": "避免过大的 WXML 节点数目", "failedTitle": "使用了过大的 WXML 节点数目", "description": "建议一个页面使用少于 1000 个 WXML 节点，节点树深度少于 30 层，子节点数不大于 60 个。一个太大的 WXML 节点树会增加内存的使用，样式重排时间也会更长。", "document": ""}, "weight": 6, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免过大的 WXML 节点数目", "headings": [{"key": "totalNodeCount", "text": "节点总数"}, {"key": "max<PERSON><PERSON><PERSON>", "text": "节点深度"}, {"key": "maxDepthNode", "text": "最深的节点"}, {"key": "max<PERSON><PERSON><PERSON><PERSON>", "text": "最大子节点数"}, {"key": "maxChildrenNode", "text": "最多子节点的节点"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "ele-active-state", "passedTitle": "避免使用 css ':active' 伪类来实现点击态", "failedTitle": "存在使用 css ':active' 伪类来实现点击态", "description": "使用 css ':active' 伪类来实现点击态，很容易触发，并且滚动或滑动时点击态不会消失，体验较差。建议使用小程序内置组件的 'hover-*' 属性来实现", "document": "https://developers.weixin.qq.com/miniprogram/dev/component/view.html"}, "weight": 8, "scoreDisplayMode": "numeric", "scoringCategory": "accessibility", "status": "failed", "title": "存在使用 css ':active' 伪类来实现点击态", "headings": [{"key": "selector", "text": "选择器"}, {"key": "file", "text": "文件"}, {"key": "page", "text": "页面"}], "details": [{"selector": ".selector--encoding-item:active", "file": "/components/encoding-selector/encoding-selector.wxss", "page": "pages/preview/preview"}, {"selector": ".history-item:active", "file": "/pages/history/history.wxss", "page": "pages/history/history"}, {"selector": ".card:active", "file": "/pages/home/<USER>", "page": "pages/home/<USER>"}], "score": 0}, {"meta": {"id": "execute-long-time", "passedTitle": "避免执行脚本的耗时过长的情况", "failedTitle": "存在执行脚本的耗时过长的情况", "description": "执行脚本的耗时过长会让用户觉得卡顿，体验较差，出现这一情况时，需要确认并优化脚本的逻辑", "document": ""}, "weight": 7, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免执行脚本的耗时过长的情况", "headings": [{"key": "method", "text": "方法名"}, {"key": "cost", "text": "耗时"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "first-meaningful-time", "passedTitle": "避免首屏时间太长的情况", "failedTitle": "存在首屏时间太长的情况", "description": "首屏时间是指用户开始看到第一屏的内容的时间，首屏时间太长会导致用户长时间看到的都是白屏，会一直等待有意义的内容展示出来。出现这一情况，应仔细检查这个过程都有哪个操作，一般来说，可能是请求数据的时间太长，或者是一次渲染的数据太大导致渲染时间太长。", "document": ""}, "weight": 6, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "failed", "title": "避免首屏时间太长的情况", "headings": [{"key": "page", "text": "页面"}, {"key": "time", "text": "首屏时间"}], "details": [{"time": 5793, "page": "pages/home/<USER>"}], "score": 0, "failedSummary": "渲染时间高达 5793 ms"}, {"meta": {"id": "image-no-cache", "passedTitle": "网络图片资源应开启 HTTP 缓存控制", "failedTitle": "存在网络图片资源未开启 HTTP 缓存控制", "description": "开启 HTTP 缓存控制后，下一次加载同样的图片，会直接从缓存读取，提升加载速度", "document": ""}, "weight": 4, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "网络图片资源应开启 HTTP 缓存控制", "headings": [{"key": "url", "text": "URL"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "image-ratio", "passedTitle": "应让图片按原图宽高比例显示", "failedTitle": "存在图片没有按原图宽高比例显示", "description": "图片若没有按原图宽高比例显示，可能导致图片歪曲，不美观，甚至导致用户识别困难。可根据情况设置 image 组件的 mode 属性，以保持原图宽高比。", "document": "https://developers.weixin.qq.com/miniprogram/dev/component/image.html"}, "weight": 4, "scoreDisplayMode": "numeric", "scoringCategory": "accessibility", "status": "passed", "title": "应让图片按原图宽高比例显示", "headings": [{"key": "img", "text": "图片地址", "type": "url"}, {"key": "display", "text": "显示宽高"}, {"key": "actual", "text": "原图宽高"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "iphonex-compatibility", "passedTitle": "固定底部的可点击组件都在iPhone X安全区域内", "failedTitle": "发现固定底部的可点击组件可能不在iPhone X安全区域内", "description": "底部的可交互组件如果渲染在iPhone X的安全区域外，容易误触Home Indicator", "document": ""}, "weight": 3, "scoreDisplayMode": "numeric", "scoringCategory": "accessibility", "status": "passed", "title": "固定底部的可点击组件都在iPhone X安全区域内", "headings": [{"key": "component", "text": "组件"}, {"key": "id", "text": "id"}, {"key": "className", "text": "className"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "js-exception", "passedTitle": "应避免出现任何 JavaScript 异常", "failedTitle": "存在 JavaScript 异常", "description": "出现 JavaScript 异常可能导致程序的交互无法进行下去，我们应当追求零异常，保证程序的高鲁棒性和高可用性", "docoment": ""}, "weight": 3, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "passed", "title": "应避免出现任何 JavaScript 异常", "headings": [{"key": "msg", "text": "错误信息"}, {"key": "stack", "text": "调用栈", "type": "stack"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "large-image", "passedTitle": "合理控制图片的大小", "failedTitle": "存在图片太大而有效显示区域较小", "description": "图片太大会增加下载时间和内存的消耗，应根据显示区域大小合理控制图片大小", "document": ""}, "weight": 4, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "合理控制图片的大小", "headings": [{"key": "url", "text": "URL", "type": "url"}, {"key": "size", "text": "大小"}, {"key": "rect", "text": "实际宽高"}, {"key": "display", "text": "显示宽高"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "overflow-scroll", "passedTitle": "滚动区域可开启惯性滚动以增强体验", "failedTitle": "滚动区域没有开启惯性滚动", "description": "惯性滚动会使滚动比较顺畅，在安卓下默认有惯性滚动，而在 iOS 下需要额外设置 `-webkit-overflow-scrolling: touch` 的样式", "document": ""}, "weight": 8, "scoreDisplayMode": "numeric", "scoringCategory": "accessibility", "status": "failed", "title": "滚动区域没有开启惯性滚动", "headings": [{"key": "selector", "text": "选择器"}, {"key": "file", "text": "文件"}, {"key": "page", "text": "页面"}], "details": [{"selector": ".text-content", "file": "/pages/preview/preview.wxss", "page": "pages/preview/preview"}, {"selector": ".history-scroll", "file": "/pages/history/history.wxss", "page": "pages/history/history"}], "score": 0}, {"meta": {"id": "render-long-time", "passedTitle": "避免渲染界面的耗时过长的情况", "failedTitle": "存在渲染界面的耗时过长的情况", "description": "渲染界面的耗时过长会让用户觉得卡顿，体验较差，出现这一情况时，需要校验下是否同时渲染的区域太大", "document": ""}, "weight": 6, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免渲染界面的耗时过长的情况", "headings": [{"key": "cost", "text": "耗时"}, {"key": "type", "text": "类型"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "request-body-cache", "passedTitle": "对网络请求做必要的缓存以避免多余的请求", "failedTitle": "存在可能没有对请求进行缓存的情况", "description": "发起网络请求总会让用户等待，可能造成不好的体验，应尽量避免多余的请求，比如对同样的请求进行缓存", "document": ""}, "weight": 2, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "对网络请求做必要的缓存以避免多余的请求", "headings": [{"key": "url", "text": "URL", "type": "url"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "request-fail", "passedTitle": "所有请求应响应正常", "failedTitle": "存在请求的响应状态码出现异常", "description": "请求失败可能导致程序的交互无法进行下去，应当保证所有请求都能成功", "document": ""}, "weight": 3, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "passed", "title": "所有请求应响应正常", "headings": [{"key": "url", "text": "URL"}, {"key": "statusCode", "text": "状态码"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "request-long-time", "passedTitle": "所有请求的耗时不应太久", "failedTitle": "存在请求的耗时太长", "description": "请求的耗时太长会让用户一直等待甚至离开，应当优化好服务器处理时间、减小回包大小，让请求快速响应", "document": ""}, "weight": 5, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "所有请求的耗时不应太久", "headings": [{"key": "url", "text": "URL"}, {"key": "time", "text": "耗时"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "resize-compatibility", "passedTitle": "窗口大小改变时进行了良好的UI适配", "failedTitle": "窗口大小改变时UI适配体验不佳", "description": "可改变窗口大小的小程序需要对不同窗口尺寸进行UI适配，以达到最佳体验", "document": ""}, "weight": 0, "scoreDisplayMode": "not-applicable", "scoringCategory": "accessibility", "status": "passed", "title": "窗口大小改变时进行了良好的UI适配", "headings": [{"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "set-data-freq", "passedTitle": "避免 setData 的调用过于频繁", "failedTitle": "存在 setData 的调用过于频繁", "description": "setData接口的调用涉及逻辑层与渲染层间的线程通过，通信过于频繁可能导致处理队列阻塞，界面渲染不及时而导致卡顿，应避免无用的频繁调用", "document": "https://developers.weixin.qq.com/miniprogram/dev/framework/performance/tips.html"}, "weight": 6, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免 setData 的调用过于频繁", "headings": [{"key": "times", "text": "每秒调用次数", "type": "none"}, {"key": "stack", "text": "调用堆栈", "type": "stack"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "set-data-large", "passedTitle": "避免 setData 的数据过大", "failedTitle": "存在 setData 的数据过大", "description": "由于小程序运行逻辑线程与渲染线程之上，setData的调用会把数据从逻辑层传到渲染层，数据太大会增加通信时间", "document": "https://developers.weixin.qq.com/miniprogram/dev/framework/performance/tips.html"}, "weight": 6, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免 setData 的数据过大", "headings": [{"key": "size", "text": "数据大小", "type": "none"}, {"key": "vars", "text": "变量名"}, {"key": "stack", "text": "调用堆栈", "type": "stack"}, {"key": "position", "text": "调用组件"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "set-data-no-binding", "passedTitle": "避免将未绑定在 WXML 的变量传入 setData", "failedTitle": "存在将未绑定在 WXML 的变量传入 setData", "description": "setData操作会引起框架处理一些渲染界面相关的工作，一个未绑定的变量意味着与界面渲染无关，传入setData会造成不必要的性能消耗", "document": "https://developers.weixin.qq.com/miniprogram/dev/framework/performance/tips.html"}, "weight": 1, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "passed", "title": "避免将未绑定在 WXML 的变量传入 setData", "headings": [{"key": "name", "text": "变量名"}, {"key": "stack", "text": "调用堆栈", "type": "stack"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "small-response-area", "passedTitle": "合理设置可点击元素的响应区域大小", "failedTitle": "存在可点击元素的响应区域过小", "description": "我们应该合理地设置好可点击元素的响应区域大小，如果过小会导致用户很难点中，体验很差", "document": ""}, "weight": 3, "scoreDisplayMode": "numeric", "scoringCategory": "accessibility", "status": "passed", "title": "合理设置可点击元素的响应区域大小", "headings": [{"key": "area", "text": "响应区域"}, {"key": "comp", "text": "监听的组件"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "timer-no-recycle", "passedTitle": "避免定时器未跟随页面回收", "failedTitle": "存在定时器未跟随页面回收", "description": "定时器是全局的，并不是跟页面绑定的，当小程序从一个页面路由到另一个页面之后，前一个页面定时器应注意手动回收", "document": ""}, "weight": 0, "scoreDisplayMode": "not-applicable", "scoringCategory": "best-practice", "status": "passed", "title": "避免定时器未跟随页面回收", "headings": [{"key": "name", "text": "定时器"}, {"key": "stack", "text": "调用堆栈", "type": "stack"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "too-much-image-request", "passedTitle": "避免短时间内发起太多的图片请求", "failedTitle": "存在短时间内发起太多的图片请求", "description": "短时间内发起太多图片请求会触发浏览器并行加载的限制，可能导致图片加载慢，用户一直处理等待。应该合理控制数量，可考虑使用 HTTP/2 多路复用、雪碧图技术、拆分域名或在屏幕外的图片使用懒加载", "document": "https://developers.weixin.qq.com/miniprogram/dev/component/image.html"}, "weight": 5, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免短时间内发起太多的图片请求", "headings": [{"key": "count", "text": "数量", "style": "min-width: 30px;"}, {"key": "url", "text": "URL", "type": "richtext"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "too-much-request", "passedTitle": "避免短时间内发起太多的请求", "failedTitle": "存在短时间内发起太多的请求", "description": "短时间内发起太多请求会触发小程序并行请求数量的限制，同时太多请求也可能导致加载慢等问题，应合理控制请求数量，可考虑使用 HTTP/2 多路复用，甚至做请求的合并等", "document": ""}, "weight": 5, "scoreDisplayMode": "numeric", "scoringCategory": "performance", "status": "passed", "title": "避免短时间内发起太多的请求", "headings": [{"key": "count", "text": "数量", "style": "min-width: 30px;"}, {"key": "url", "text": "URL", "type": "richtext"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "use-https", "passedTitle": "所有资源请求都建议使用 HTTPS", "failedTitle": "所有资源请求都建议使用 HTTPS", "description": "使用 HTTPS，可以让你的小程序更加安全，而 HTTP 是明文传输的，存在可能被篡改内容的风险", "document": ""}, "weight": 1, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "passed", "title": "所有资源请求都建议使用 HTTPS", "headings": [{"key": "url", "text": "URL", "type": "url"}, {"key": "page", "text": "页面"}], "details": [], "score": 100}, {"meta": {"id": "color-contrast", "passedTitle": "文字颜色与背景色搭配较好，适宜的颜色对比度更方便用户阅读", "failedTitle": "文字颜色与背景色搭配较差，两者颜色过于接近会让用户无法或难以阅读", "description": "文字颜色与背景色需要搭配得当，适宜的颜色对比度可以让用户更好地阅读，提升小程序的用户体验", "document": "https://developers.weixin.qq.com/miniprogram/design/index.html"}, "weight": 0, "scoreDisplayMode": "not-accurate", "scoringCategory": "accessibility", "status": "failed", "title": "文字颜色与背景色搭配较差，两者颜色过于接近会让用户无法或难以阅读", "headings": [{"key": "area", "text": "显示区域"}, {"key": "text", "text": "文字"}, {"key": "bgColor", "text": "背景颜色", "type": "richtext"}, {"key": "page", "text": "页面"}], "details": [{"area": "<view   class=\"container\">", "text": "TXT编码...、轻松解决乱...", "bgColor": "<div style=\"background-color: rgba(255, 249, 251, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/home/<USER>"}, {"area": "<view   bindtap=\"onRecentTap\" class=\"recent-item card\" data-item=\"[object Object]\">", "text": "222.t...", "bgColor": "<div style=\"background-color: rgba(255, 255, 255, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/home/<USER>"}, {"area": "<view   bindtap=\"showEncodingSelector\" class=\"encoding-btn\">", "text": "更改、▼", "bgColor": "<div style=\"background-color: rgba(255, 249, 251, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/preview/preview"}, {"area": "<input   bindinput=\"onSearchInput\" class=\"selector--search-input\" placeholder=\"搜索编码\" value=\"\">", "text": "搜索编码", "bgColor": "<div style=\"background-color: rgba(255, 249, 251, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/preview/preview"}, {"area": "<view   class=\"file-info card\">", "text": "utf-8", "bgColor": "<div style=\"background-color: rgba(255, 255, 255, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/preview/preview"}, {"area": "<view   bindtap=\"onEncodingSelect\" class=\"selector--encoding-item selector--selected\" data-encoding=\"utf-8\">", "text": "置信度 9...、✓", "bgColor": "<div style=\"background-color: rgba(255, 249, 251, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/preview/preview"}, {"area": "<view   class=\"search-input-wrapper\">", "text": "搜索文件名...", "bgColor": "<div style=\"background-color: rgba(255, 255, 255, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/history/history"}, {"area": "<view   class=\"encoding-tag\">", "text": "utf-8", "bgColor": "<div style=\"background-color: rgba(255, 249, 251, 1); width: inherit; height: 20px; border: 1px solid black; text-align: center;\"></div>", "page": "pages/history/history"}], "score": 0, "showIgnore": true}, {"meta": {"id": "recomended-version", "passedTitle": "未发现正在使用组件/API的最低支持版本高于线上最低基础库版本", "failedTitle": "发现正在使用组件/API的最低支持版本高于线上最低基础库版本", "description": "使用低版本不支持的组件或API可能导致小程序无法正常运行，推荐使用支持对应组件/API的最低或更高版本", "document": "https://developers.weixin.qq.com/miniprogram/dev/framework/compatibility.html#%E8%AE%BE%E7%BD%AE%E6%9C%80%E4%BD%8E%E5%9F%BA%E7%A1%80%E5%BA%93%E7%89%88%E6%9C%AC"}, "weight": 0, "scoreDisplayMode": "not-applicable", "scoringCategory": "best-practice", "status": "failed", "title": "发现正在使用组件/API的最低支持版本高于线上最低基础库版本", "headings": [{"key": "descriptor", "text": "组件/API"}, {"key": "stack", "text": "调用栈", "type": "stack"}, {"key": "page", "text": "页面"}, {"key": "version", "text": "最低支持版本"}], "details": [{"descriptor": "view.aria-label", "version": "2.5.0", "page": "pages/home/<USER>"}, {"descriptor": "view.aria-role", "version": "2.5.0", "page": "pages/home/<USER>"}, {"descriptor": "view.aria-label", "version": "2.5.0", "page": "pages/preview/preview"}, {"descriptor": "view.aria-role", "version": "2.5.0", "page": "pages/preview/preview"}, {"descriptor": "view.aria-label", "version": "2.5.0", "page": "pages/history/history"}, {"descriptor": "view.aria-role", "version": "2.5.0", "page": "pages/history/history"}], "score": 100, "failedSummary": "使用了 2.5.0 版本才支持的组件/API"}, {"meta": {"id": "unused-css", "passedTitle": "避免引入大量未被使用的样式", "failedTitle": "wxss 覆盖率较低，存在大量未使用的样式", "description": "我们应该按需引入 wxss 资源，如果小程序中存在大量未使用的样式，会增加小程序包体积大小，从而在一定程度上影响加载速度", "document": ""}, "weight": 0, "scoreDisplayMode": "not-accurate", "scoringCategory": "best-practice", "status": "failed", "title": "wxss 覆盖率较低，存在大量未使用的样式", "headings": [{"key": "file", "text": "文件名"}, {"key": "original", "text": "原文件大小"}, {"key": "savings", "text": "可节省"}], "details": [{"file": "/pages/preview/preview.wxss", "original": "6.4 KB", "savings": "2.7 KB(42%)"}, {"file": "/components/text-toolbar/text-toolbar.wxss", "original": "5.1 KB", "savings": "4.2 KB(84%)"}], "score": 0, "failedSummary": "大约可以节省 7 KB", "showIgnore": true}, {"meta": {"id": "unused-page", "passedTitle": "避免将不可能被访问到的页面打包在小程序包里", "failedTitle": "存在将不可能被访问到的页面打包在小程序包里", "description": "小程序的包大小会影响加载时间，应该尽量控制包体积大小，避免将不会被使用的文件打包进去", "document": ""}, "weight": 0, "scoreDisplayMode": "not-accurate", "scoringCategory": "best-practice", "status": "passed", "title": "避免将不可能被访问到的页面打包在小程序包里", "headings": [{"key": "file", "text": "文件"}], "details": [], "score": 100}, {"meta": {"id": "unused-component", "passedTitle": "避免将多余的组件声明在usingComponents中", "failedTitle": "usingComponents中存在多余的组件声明", "description": "组件/页面的usingComponents会影响启动速度，应避免将多余的组件声明在usingComponents中", "document": ""}, "weight": 1, "scoreDisplayMode": "numeric", "scoringCategory": "best-practice", "status": "failed", "title": "usingComponents中存在多余的组件声明", "headings": [{"key": "component", "text": "声明但未使用的组件"}, {"key": "user", "text": "引用者"}], "details": [{"user": "/pages/preview/preview.wxml", "component": "<text-toolbar>"}], "score": 0, "failedSummary": "usingComponents中存在1个多余的组件声明"}], "categories": [{"type": "performance", "text": "性能", "score": 90, "level": "A"}, {"type": "accessibility", "text": "体验", "score": 38, "level": "D"}, {"type": "best-practice", "text": "最佳实践", "score": 91, "level": "A"}]}