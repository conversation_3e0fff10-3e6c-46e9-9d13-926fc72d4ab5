/**
 * 全局主题样式变量
 * 可爱风格设计
 */

/* 主题色 */
page {
  /* 主色调 - 粉色系 */
  --primary-color: #F48FB1;
  --primary-light: #FFF9FB;
  --primary-dark: #BF5F82;

  /* 强调色 */
  --accent-color: #FFCDD2;
  --accent-light: #FFEBEE;

  /* 功能色 */
  --success-color: #81C784;
  --warning-color: #FFD54F;
  --error-color: #E57373;

  /* 文本色 */
  --text-primary: #333333;
  --text-secondary: #757575;
  --text-hint: #9E9E9E;

  /* 背景色 */
  --background-color: #FFFFFF;
  --card-background: #FFFFFF;

  /* 边框和分割线 */
  --border-color: #F0F0F0;
  --divider-color: #EEEEEE;

  /* 尺寸规范 */
  --border-radius-sm: 8rpx;
  --border-radius-md: 16rpx;
  --border-radius-lg: 24rpx;

  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);

  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;

  /* 字体 */
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-md: 32rpx;
  --font-size-lg: 36rpx;
  --font-size-xl: 40rpx;
}

/* 全局通用样式类 */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  padding: var(--spacing-sm) var(--spacing-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  padding: var(--spacing-sm) var(--spacing-lg);
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.divider {
  height: 2rpx;
  background-color: var(--divider-color);
  width: 100%;
  margin: var(--spacing-md) 0;
} 