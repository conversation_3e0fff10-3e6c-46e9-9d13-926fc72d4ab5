<navigation-bar title="历史记录" showBack="{{true}}"></navigation-bar>

<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/assets/images/icons/search.png" mode="aspectFit"></image>
      <input
        class="search-input"
        placeholder="搜索文件名..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchConfirm"
      />
      <view wx:if="{{searchKeyword}}" class="clear-search-btn" bindtap="onClearSearch">
        <image src="/assets/images/icons/close.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 清空历史记录图标 -->
    <view wx:if="{{filteredHistoryFiles.length > 0}}" class="clear-history-icon" bindtap="onClearHistory" hover-class="clear-history-hover" hover-stay-time="100">
      <image src="/assets/images/icons/delete.png" mode="aspectFit"></image>
    </view>

    <scroll-view class="history-scroll" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
      <!-- 历史记录列表 -->
      <view class="history-list" wx:if="{{filteredHistoryFiles.length > 0}}">
        <view class="history-item" wx:for="{{filteredHistoryFiles}}" wx:key="id" bindtap="onHistoryItemTap" data-item="{{item}}">
          <view class="item-header">
            <view class="file-name text-primary">{{item.fileName}}</view>
            <view class="file-size text-secondary">{{item.fileSize}}</view>
          </view>
          <view class="item-info">
            <view class="encoding-tag">{{item.encoding}}</view>
            <view class="date text-secondary">{{item.relativeTime}}</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:else>
        <image class="empty-image" src="/assets/images/empty-history.png" mode="aspectFit"></image>
        <view class="empty-text">{{searchKeyword ? '未找到相关文件' : '暂无历史记录'}}</view>
        <view class="empty-desc">{{searchKeyword ? '尝试其他关键词' : '您打开的文件将显示在这里'}}</view>
      </view>
    </scroll-view>
  </view>
</view> 