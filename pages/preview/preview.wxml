<navigation-bar title="文件预览" showBack="{{true}}"></navigation-bar>

<view class="container {{themeMode === 'dark' ? 'dark-theme' : ''}}">
  <!-- 顶部信息栏 -->
  <view class="file-info card">
    <view class="file-header flex-between">
      <view class="file-name">{{fileName}}</view>
      <view class="file-size">{{fileSize}}</view>
    </view>
    <view class="encoding-wrapper flex-between">
      <view class="encoding-info">
        <text>编码：</text>
        <text class="encoding-value">{{currentEncoding}}</text>
      </view>
      <view class="encoding-btn" bindtap="showEncodingSelector">
        <text>更改</text>
        <text class="encoding-arrow">▼</text>
      </view>
    </view>
  </view>
  
  <!-- 中央内容区 -->
  <view class="content-card card">    
    <!-- 右上角操作图标 -->
    <view class="action-icons">
      <view class="action-icon save-icon" bindtap="onShareTap">
        <image src="/assets/images/icons/save.png" mode="aspectFit"></image>
      </view>
      <view class="action-icon share-icon" bindtap="onShareTap">
        <image src="/assets/images/icons/share.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 文本内容 -->
    <view wx:if="{{!isLoading && !loadError && !showFullContent && contentLineCount > maxDisplayLines}}" class="content-preview">
      <view class="text-container {{wordWrap ? '' : 'no-wrap'}}" style="font-size: {{fontSize}}px; line-height: {{lineHeight}};">
        <block wx:if="{{!editMode}}">
          <text user-select="true">{{previewContent}}</text>
        </block>
        <block wx:else>
          <textarea 
            class="text-editor preview-mode" 
            value="{{previewContent}}" 
            bindinput="onContentChange"
            disabled="{{true}}"
            auto-height="{{true}}"
          ></textarea>
        </block>
      </view>
      <view class="show-more-btn" bindtap="toggleContentDisplay">
        <text>显示更多 ▼</text>
      </view>
    </view>
    
    <scroll-view
      wx:elif="{{!isLoading && !loadError}}"
      class="text-content {{showFullContent ? 'full-content' : ''}}"
      scroll-y="{{contentLineCount > maxDisplayLines}}"
      style="font-size: {{fontSize}}px; line-height: {{lineHeight}};"
      enhanced="{{true}}"
      enable-flex="{{true}}"
      show-scrollbar="{{false}}"
    >
      <view class="text-container {{wordWrap ? '' : 'no-wrap'}}">
        <block wx:if="{{!editMode}}">
          <text user-select="true">{{fileContent}}</text>
        </block>
        <block wx:else>
          <textarea 
            class="text-editor" 
            value="{{fileContent}}" 
            bindinput="onContentChange"
            maxlength="-1"
            auto-height="{{true}}"
            show-confirm-bar="{{false}}"
          ></textarea>
        </block>
      </view>
      
      <view wx:if="{{showFullContent && contentLineCount > maxDisplayLines}}" class="show-less-btn" bindtap="toggleContentDisplay">
        <text>收起 ▲</text>
      </view>
    </scroll-view>
    
    <!-- 读取状态显示 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在加载文件内容...</view>
    </view>
    
    <!-- 错误显示 -->
    <view class="error-container" wx:if="{{loadError}}">
      <icon type="warn" size="40" color="#E57373"></icon>
      <view class="error-text">{{errorMessage}}</view>
      <button class="btn-primary" bindtap="tryAnotherEncoding">选择其他编码</button>
    </view>
  </view>
</view>

<encoding-selector 
  show="{{showEncodingSelector}}" 
  encodings="{{availableEncodings}}" 
  selectedEncoding="{{currentEncoding}}"
  detectedEncoding="{{detectedEncoding}}"
  detectedConfidence="{{detectedConfidence}}"
  bind:select="onEncodingChange"
  bind:close="hideEncodingSelector">
</encoding-selector>