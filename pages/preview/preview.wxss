@import "/styles/theme.wxss";

/* 为整个页面设置布局 */
page {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  padding: 30rpx;
  padding-top: 20rpx;
  box-sizing: border-box;
  background-color: var(--primary-light);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  overflow: hidden;
}

/* 深色主题 */
.dark-theme {
  --primary-light: #1f1f1f;
  --card-background: #2d2d2d;
  --text-primary: #f0f0f0;
  --text-secondary: #b0b0b0;
  --divider-color: #444444;
  --border-color: #555555;
}

/* 文件信息区域 */
.file-info {
  padding: 24rpx;
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border: 2rpx solid var(--border-color);
}

.file-header {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.file-name {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
  padding-right: 20rpx;
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

.encoding-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.encoding-info {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  flex-wrap: wrap;
}

.encoding-value {
  color: var(--primary-color);
  font-weight: bold;
  margin: 0 8rpx;
}

.confidence-tag {
  font-size: var(--font-size-xs);
  color: white;
  background-color: var(--warning-color);
  padding: 4rpx 12rpx;
  border-radius: var(--border-radius-sm);
  margin-left: 12rpx;
}

.encoding-btn {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--primary-dark);
  background-color: #FFFFFF;
  border: 2rpx solid var(--primary-color);
  padding: 8rpx 20rpx;
  border-radius: var(--border-radius-md);
}

.encoding-arrow {
  font-size: var(--font-size-xs);
  margin-left: 6rpx;
}

/* 内容卡片 */
.content-card {
  flex: 1;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 内容预览 */
.content-preview {
  padding: 24rpx;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 显示更多按钮 */
.show-more-btn {
  height: auto;
  padding: 20rpx 0;
  margin-top: 10rpx;
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  border-top: 2rpx dashed var(--divider-color);
  width: 100%;
  text-align: center;
}

.show-more-icon {
  margin-left: 8rpx;
}

/* 显示收起按钮 */
.show-less-btn {
  height: auto;
  padding: 20rpx 0;
  margin-top: 10rpx;
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  border-top: 2rpx dashed var(--divider-color);
  width: 100%;
  text-align: center;
}

.show-less-icon {
  margin-left: 8rpx;
}

/* 文本内容 */
.text-content {
  flex: 1;
  padding: 24rpx;
  height: auto;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 开启iOS惯性滚动 */
}

.text-content.full-content {
  /* 不需要特殊处理 */
}

.text-container {
  width: 100%;
}

.text-container.no-wrap {
  white-space: nowrap;
  width: max-content;
  min-width: 100%;
}

/* 编辑模式 */
.text-editor {
  width: 100%;
  min-height: 100%;
  color: var(--text-primary);
  line-height: inherit;
  font-size: inherit;
}

.text-editor.preview-mode {
  pointer-events: none;
}

/* 加载状态 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.dark-theme .loading-container {
  background-color: rgba(45, 45, 45, 0.8);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid var(--primary-light);
  border-top: 6rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 错误状态 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--card-background);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  z-index: 10;
}

.error-text {
  font-size: var(--font-size-md);
  color: var(--error-color);
  text-align: center;
  margin: 30rpx 0;
}

/* 右上角操作图标 */
.action-icons {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 16rpx;
  z-index: 20;
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: var(--card-background);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.action-icon image {
  width: 40rpx;
  height: 40rpx;
}

/* 移除未使用的搜索高亮样式 */