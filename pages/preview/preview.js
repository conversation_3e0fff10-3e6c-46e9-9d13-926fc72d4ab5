// pages/preview/preview.js
const app = getApp();

Page({
  data: {
    filePath: '',
    fileName: '',
    fileSize: '0 KB',
    fileContent: '',
    isLoading: true,
    loadError: false,
    errorMessage: '',
    
    // 编码相关
    detectedEncoding: '',
    detectedConfidence: 0,
    confidencePercent: '0',
    currentEncoding: '',
    availableEncodings: [
      'UTF-8', 'GBK', 'GB18030', 'Big5', 
      'Shift_JIS', 'EUC-JP', 'EUC-KR',
      'ISO-8859-1', 'Windows-1252'
    ],
    showEncodingSelector: false,
    
    // 原始文件数据
    fileData: null,
    
    // 显示控制
    themeMode: 'light',  // light或dark
    fontSize: 14,        // 字体大小，单位px
    lineHeight: 1.6,     // 行高倍数
    wordWrap: true,      // 自动换行
    editMode: false,     // 编辑模式
    
    // 内容行数控制
    contentLineCount: 0,
    maxDisplayLines: 30,  // 默认值，将根据屏幕高度动态调整
    showFullContent: false, // 是否显示完整内容
    previewContent: '',     // 预览内容（截断的）
    
    // 编辑内容是否有改动
    contentChanged: false
  },

  onLoad: function(options) {
    const filePath = decodeURIComponent(options.filePath || '');
    const fileName = decodeURIComponent(options.fileName || '未命名.txt');
    const fromHistory = options.fromHistory === 'true';
    
    this.setData({
      filePath,
      fileName,
      isLoading: true,
      loadError: false,
      fromHistory: fromHistory
    });
    
    // 计算适合当前设备的显示行数
    this.calculateOptimalDisplayLines();
    
    // 修改为先预加载文件，然后让用户选择编码
    this.preloadFile(filePath);
    
    // 尝试加载用户设置
    this.loadUserSettings();
  },
  
  // 预加载文件并检测编码
  preloadFile: function(filePath) {
    const that = this;
    const encodingUtil = require('../../utils/encoding.js');

    this.setData({
      isLoading: true,
      loadError: false,
      errorMessage: ''
    });
    
    // 获取文件信息
    wx.getFileSystemManager().getFileInfo({
      filePath: filePath,
      success(fileInfo) {
        let size = fileInfo.size;
        let sizeStr = '未知大小';

        if (typeof size === 'number' && !isNaN(size)) {
          if (size < 1024) {
            sizeStr = size + ' B';
          } else if (size < 1024 * 1024) {
            sizeStr = (size / 1024).toFixed(1) + ' KB';
          } else {
            sizeStr = (size / (1024 * 1024)).toFixed(1) + ' MB';
          }
        }
        
        that.setData({ fileSize: sizeStr });
      },
      fail(err) {
        console.error('获取文件信息失败:', err);
        that.setData({ fileSize: '未知大小' });
      }
    });
    
    // 读取文件
    wx.getFileSystemManager().readFile({
      filePath: filePath,
      success(res) {
        that.setData({ fileData: res.data });
        
        // 调用后端API检测编码
        encodingUtil.detectEncoding(res.data)
          .then(result => {
            const detectedEncoding = result.encoding || 'UTF-8';
            const confidence = result.confidence || 0;
            const confidencePercent = Math.round(confidence * 100).toString();

            that.setData({
              detectedEncoding: detectedEncoding,
              detectedConfidence: confidence,
              confidencePercent: confidencePercent
            });

            that.showEncodingSelector();
          })
          .catch(error => {
            console.error('编码检测失败:', error);
            that.setData({
              isLoading: false,
              loadError: true,
              errorMessage: '编码检测失败，请检查网络连接: ' + error.message
            });
          });
      },
      fail(err) {
        console.error('读取文件失败:', err);
        that.setData({
          isLoading: false,
          loadError: true,
          errorMessage: '读取文件失败: ' + err.errMsg
        });
      }
    });
  },
  
  // 使用指定编码加载文件内容
  loadFileContent: function(encoding) {
    const that = this;
    const encodingUtil = require('../../utils/encoding.js');

    this.setData({
      isLoading: true,
      loadError: false,
      errorMessage: '',
      currentEncoding: encoding
    });
    
    // 使用指定编码读取文件
    encodingUtil.readFileWithEncoding(this.data.fileData, encoding)
      .then(result => {
        // 使用检测到的源编码
        const sourceEncoding = result.source_encoding;

        // 如果有转换后的文本，使用它；否则使用原始文本
        const content = result.converted_text || result.original_text;

        // 更新当前编码
        const actualEncoding = result.target_encoding || sourceEncoding;

        const lineCount = that.calculateLineCount(content);
        const previewContent = that.getPreviewContent(content, that.data.maxDisplayLines);

        that.setData({
          fileContent: content,
          contentLineCount: lineCount,
          previewContent: previewContent,
          isLoading: false,
          currentEncoding: actualEncoding
        });

        // 添加到历史记录
        if (!that.data.fromHistory) {
          const storage = require('../../utils/storage.js');
          storage.addHistory({
            fileName: that.data.fileName,
            filePath: that.data.filePath,
            encoding: actualEncoding,
            date: new Date().toISOString(),
            fileSize: that.data.fileSize
          });
        }
      })
      .catch(error => {
        console.error('文件读取失败:', error);
        that.setData({
          isLoading: false,
          loadError: true,
          errorMessage: '文件读取失败，请检查网络连接: ' + error.message
        });
      });
  },
  
  // 计算文本的行数
  calculateLineCount: function(text) {
    if (!text) return 0;
    // 按照换行符分割文本并计算行数
    return text.split('\n').length;
  },
  
  // 获取预览内容（截取指定行数）
  getPreviewContent: function(text, maxLines) {
    if (!text) return '';
    
    const lines = text.split('\n');
    if (lines.length <= maxLines) return text;
    
    // 只返回前maxLines行，并添加省略号
    return lines.slice(0, maxLines).join('\n') + '\n...';
  },
  
  // 根据屏幕高度计算最佳显示行数
  calculateOptimalDisplayLines: function() {
    try {
      // 获取系统信息 - 使用新的API替代废弃的getSystemInfoSync
      const windowInfo = wx.getWindowInfo();
      const screenHeight = windowInfo.windowHeight;
      
      // 计算估计行数
      // 考虑因素：屏幕高度、字体大小、行高、页面上其他元素占用的空间
      // 页面上其他元素包括：导航栏、文件信息区域、padding等
      const fontSize = this.data.fontSize;
      const lineHeight = this.data.lineHeight;
      
      // 估算其他UI元素占用的高度（单位：px）
      const navigationBarHeight = 44; // 标准导航栏高度
      const statusBarHeight = systemInfo.statusBarHeight || 20; // 状态栏高度
      const fileInfoHeight = 120; // 估算文件信息区域高度
      const paddings = 60; // 估算内外边距总和
      const buttonHeight = 50; // 显示更多按钮高度
      
      // 计算可用于显示文本的空间
      const availableHeight = screenHeight - (navigationBarHeight + statusBarHeight + fileInfoHeight + paddings + buttonHeight);
      
      // 计算每行文本的高度（fontSize * lineHeight）
      const lineHeightPx = fontSize * lineHeight;
      
      // 计算可显示的行数并取整
      let optimalLines = Math.floor(availableHeight / lineHeightPx);
      
      // 设置合理范围限制
      optimalLines = Math.max(15, Math.min(50, optimalLines));
      
      console.log('计算得到最佳显示行数:', optimalLines, '屏幕高度:', screenHeight);
      
      this.setData({
        maxDisplayLines: optimalLines
      });
    } catch (error) {
      console.error('计算最佳显示行数失败:', error);
      // 保持默认值
    }
  },
  
  // 加载用户设置
  loadUserSettings: function() {
    try {
      const settings = wx.getStorageSync('txt_reader_settings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        this.setData({
          themeMode: parsedSettings.themeMode || 'light',
          fontSize: parsedSettings.fontSize || 14,
          lineHeight: parsedSettings.lineHeight || 1.6,
          wordWrap: parsedSettings.wordWrap !== false
        });
        
        // 字体大小或行高变化时重新计算最佳显示行数
        this.calculateOptimalDisplayLines();
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  },
  
  // 保存用户设置
  saveUserSettings: function() {
    try {
      const settings = {
        themeMode: this.data.themeMode,
        fontSize: this.data.fontSize,
        lineHeight: this.data.lineHeight,
        wordWrap: this.data.wordWrap
      };
      wx.setStorageSync('txt_reader_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  },
  
  // 切换显示完整内容/预览
  toggleContentDisplay: function() {
    this.setData({
      showFullContent: !this.data.showFullContent
    });
  },
  
  // 显示编码选择器
  showEncodingSelector: function() {
    this.setData({
      showEncodingSelector: true
    });
  },
  
  // 处理用户关闭编码选择器
  hideEncodingSelector: function() {
    if (!this.data.fileContent) {
      const detectedEncoding = this.data.detectedEncoding || 'UTF-8';
      this.loadFileContent(detectedEncoding);
    }
    
    this.setData({
      showEncodingSelector: false
    });
  },
  
  // 处理编码变更（从当前编码切换到新编码）
  onEncodingChange: function(e) {
    const newEncoding = e.detail.encoding;

    this.setData({
      showEncodingSelector: false
    });

    // 始终使用原始文件数据，只是以不同编码预览
    this.loadFileContent(newEncoding);
  },

  // 编码转换
  convertToNewEncoding: function(currentEncoding, targetEncoding) {
    const that = this;
    const encodingUtil = require('../../utils/encoding.js');

    this.setData({
      isLoading: true,
      loadError: false,
      errorMessage: '',
      currentEncoding: targetEncoding
    });

    // 调用编码转换API
    encodingUtil.convertEncoding(this.data.fileData, currentEncoding, targetEncoding)
      .then(result => {
        const content = result.text_content;
        const lineCount = that.calculateLineCount(content);
        const previewContent = that.getPreviewContent(content, that.data.maxDisplayLines);

        that.setData({
          fileContent: content,
          contentLineCount: lineCount,
          previewContent: previewContent,
          isLoading: false
        });

        wx.showToast({
          title: `已转换为 ${targetEncoding}`,
          icon: 'success',
          duration: 1500
        });
      })
      .catch(error => {
        console.error('编码转换失败:', error);
        that.setData({
          isLoading: false,
          loadError: true,
          errorMessage: '编码转换失败: ' + error.message,
          currentEncoding: currentEncoding // 恢复原编码
        });
      });
  },
  
  tryAnotherEncoding: function() {
    this.showEncodingSelector();
  },
  
  // 删除 text-toolbar 相关的处理函数
  
  // 内容变更处理
  onContentChange: function(e) {
    const newContent = e.detail.value;
    
    // 重新计算行数和预览内容
    const lineCount = this.calculateLineCount(newContent);
    const previewContent = this.getPreviewContent(newContent, this.data.maxDisplayLines);
    
    this.setData({
      fileContent: newContent,
      contentLineCount: lineCount,
      previewContent: previewContent,
      contentChanged: true
    });
  },
  
  // 复制文本内容
  onCopyContent: function() {
    const content = this.data.fileContent;
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },
  
  onShareTap: function() {
    const that = this;
    
    if (this.data.loadError) {
      wx.showToast({
        title: '无法分享，请先成功打开文件',
        icon: 'none'
      });
      return;
    }
    
    const fs = wx.getFileSystemManager();
    const tempFilePath = `${wx.env.USER_DATA_PATH}/share_${Date.now()}.txt`;
    
    try {
      const currentEncoding = that.data.currentEncoding || 'UTF-8';
      const originalName = that.data.fileName;
      let newFileName = originalName;

      const lastDotIndex = originalName.lastIndexOf('.');
      if (lastDotIndex !== -1) {
        const nameWithoutExt = originalName.substring(0, lastDotIndex);
        const extension = originalName.substring(lastDotIndex);
        newFileName = `${nameWithoutExt}_${currentEncoding}${extension}`;
      } else {
        newFileName = `${originalName}_${currentEncoding}.txt`;
      }
      
      // 直接写入UTF-8编码的文本
      fs.writeFileSync(tempFilePath, that.data.fileContent, 'utf8');

      wx.shareFileMessage({
        filePath: tempFilePath,
        fileName: newFileName,
        success() {
          console.log('分享成功');
        },
        fail(err) {
          console.error('分享失败:', err);
          wx.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('准备分享文件失败:', error);
      wx.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  },
  
  onShareAppMessage: function() {
    return {
      title: 'TXT编码转换器 - 轻松解决乱码问题',
      path: '/pages/home/<USER>'
    };
  }
}); 