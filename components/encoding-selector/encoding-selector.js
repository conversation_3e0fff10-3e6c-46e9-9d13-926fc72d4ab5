// components/encoding-selector/encoding-selector.js

// 常量定义
const CONTAINER_VH = 0.7; // 弹窗高度占视窗的 70%
const FIXED_TOP_PX = 160; // 头部固定区域（标题+搜索框+间距）高度估算，单位px

Component({
  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 所有可用的编码列表
    encodings: {
      type: Array,
      value: []
    },
    // 当前选中的编码
    selectedEncoding: {
      type: String,
      value: ''
    },
    // 检测到的编码
    detectedEncoding: {
      type: String,
      value: ''
    },
    // 检测置信度
    detectedConfidence: {
      type: Number,
      value: 0
    }
  },

  data: {
    searchText: '',
    filteredEncodings: [],
    confidencePercent: '0', // 用于显示的置信度百分比
    listHeight: 0 // 新增：列表高度，单位px
  },

  observers: {
    'encodings, searchText': function(encodings, searchText) {
      this.filterEncodings(encodings, searchText);
    },
    'show': function(show) {
      if (show) {
        // 重置搜索框
        this.setData({
          searchText: ''
        });
        // 重新过滤编码列表
        this.filterEncodings(this.properties.encodings, '');
        // 计算列表高度
        this.calculateListHeight();
      }
    },
    'detectedConfidence': function(confidence) {
      // 监听置信度变化，更新百分比显示
      this.updateConfidencePercent(confidence);
    }
  },

  lifetimes: {
    attached: function() {
      // 组件创建时，初始化置信度百分比
      this.updateConfidencePercent(this.properties.detectedConfidence);
      // 计算列表高度
      this.calculateListHeight();
    }
  },

  methods: {
    // 计算列表区域高度
    calculateListHeight: function() {
      try {
        // 使用新的API替代废弃的getSystemInfoSync
        const windowInfo = wx.getWindowInfo();
        const windowHeight = windowInfo.windowHeight;
        const containerHeight = Math.round(windowHeight * CONTAINER_VH);
        const listHeight = containerHeight - FIXED_TOP_PX;
        
        this.setData({ 
          listHeight: Math.max(100, listHeight) // 确保至少有100px高度
        });
        
        console.log('编码选择器列表高度计算：', {
          windowHeight,
          containerHeight,
          fixedTopHeight: FIXED_TOP_PX,
          listHeight: this.data.listHeight
        });
      } catch (error) {
        console.error('计算列表高度失败:', error);
        // 设置一个合理的默认值
        this.setData({ listHeight: 300 });
      }
    },
    
    // 更新置信度百分比显示
    updateConfidencePercent: function(confidence) {
      let percent = '0';
      if (typeof confidence === 'number' && !isNaN(confidence)) {
        percent = Math.round(confidence * 100).toString();
      }
      this.setData({
        confidencePercent: percent
      });
    },
    
    // 关闭选择器
    onClose: function() {
      this.triggerEvent('close');
    },
    
    // 选择编码
    onEncodingSelect: function(e) {
      const encoding = e.currentTarget.dataset.encoding;
      this.triggerEvent('select', { encoding });
    },
    
    // 搜索输入
    onSearchInput: function(e) {
      const searchText = e.detail.value;
      this.setData({
        searchText: searchText
      });
    },
    
    // 过滤编码列表
    filterEncodings: function(encodings, searchText) {
      if (!encodings || encodings.length === 0) {
        this.setData({
          filteredEncodings: []
        });
        return;
      }
      
      let filtered = encodings;
      
      // 如果有搜索文本，进行过滤
      if (searchText && searchText.trim() !== '') {
        const searchLower = searchText.toLowerCase();
        filtered = encodings.filter(encoding => 
          encoding.toLowerCase().includes(searchLower)
        );
      }
      
      // 将检测到的编码从列表中移除（因为已经单独显示在顶部）
      const detectedEncoding = this.properties.detectedEncoding;
      if (detectedEncoding) {
        filtered = filtered.filter(encoding => encoding !== detectedEncoding);
      }
      
      // 按照常用程度和字母顺序排序
      const commonEncodings = ['UTF-8', 'GBK', 'GB18030', 'Big5', 'Shift_JIS'];
      
      filtered.sort((a, b) => {
        const aIsCommon = commonEncodings.includes(a);
        const bIsCommon = commonEncodings.includes(b);
        
        if (aIsCommon && !bIsCommon) return -1;
        if (!aIsCommon && bIsCommon) return 1;
        
        // 如果都是常用编码或都不是常用编码，按字母顺序排序
        return a.localeCompare(b);
      });
      
      this.setData({
        filteredEncodings: filtered
      });
    }
  }
}); 