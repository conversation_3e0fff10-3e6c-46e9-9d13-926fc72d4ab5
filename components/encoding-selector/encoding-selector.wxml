<!-- 遮罩层 -->
<view class="encoding-selector-mask {{show ? 'show' : ''}}" bindtap="onClose"></view>

<!-- 选择器容器 -->
<view class="encoding-selector {{show ? 'show' : ''}}">
  <!-- 选择器头部 -->
  <view class="selector-header">
    <view class="selector-title">选择打开文件的编码</view>
    <view class="close-btn" bindtap="onClose">×</view>
  </view>
  
  <!-- 搜索框 -->
  <view class="search-box">
    <input class="search-input" placeholder="搜索编码" bindinput="onSearchInput" value="{{searchText}}"/>
    <view class="search-icon">🔍</view>
  </view>
  
  <!-- 编码列表 -->
  <scroll-view class="encoding-list" scroll-y style="height: {{listHeight}}px;">
    <!-- 检测到的编码 -->
    <block wx:if="{{detectedEncoding}}">
      <view class="encoding-section">
        <view class="section-title">检测到的编码</view>
        <view class="encoding-item {{selectedEncoding === detectedEncoding ? 'selected' : ''}}"
              data-encoding="{{detectedEncoding}}"
              bindtap="onEncodingSelect"
              hover-class="encoding-item-hover"
              hover-stay-time="100">
          <view class="encoding-name">{{detectedEncoding}}</view>
          <view class="encoding-confidence">
            置信度 {{confidencePercent}}%
            <text wx:if="{{selectedEncoding === detectedEncoding}}" class="check-icon">✓</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 其他编码选项 -->
    <view class="encoding-section">
      <view class="section-title">其他编码</view>
      <block wx:for="{{filteredEncodings}}" wx:key="*this">
        <view class="encoding-item {{selectedEncoding === item ? 'selected' : ''}}"
              data-encoding="{{item}}"
              bindtap="onEncodingSelect"
              hover-class="encoding-item-hover"
              hover-stay-time="100">
          <view class="encoding-name">{{item}}</view>
          <text wx:if="{{selectedEncoding === item}}" class="check-icon">✓</text>
        </view>
      </block>
    </view>
  </scroll-view>
</view>
