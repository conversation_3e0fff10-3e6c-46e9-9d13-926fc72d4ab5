@import "/styles/theme.wxss";

.encoding-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.encoding-selector-mask.show {
  visibility: visible;
  opacity: 1;
}

.encoding-selector {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s;
  padding: 30rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  max-height: 70vh;
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.encoding-selector.show {
  transform: translateY(0);
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.selector-title {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--text-primary);
}

.close-btn {
  font-size: 48rpx;
  color: var(--text-secondary);
  line-height: 1;
  padding: 10rpx;
}

.search-box {
  position: relative;
  margin-bottom: 20rpx;
}

.search-input {
  background-color: #FFFFFF;
  border: 2rpx solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 16rpx 60rpx 16rpx 20rpx;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  width: 100%;
  height: 80rpx; /* 增加高度 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
  line-height: 48rpx; /* 添加行高 */
}

.search-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

.encoding-list {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 开启iOS惯性滚动 */
}

.encoding-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.encoding-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: var(--border-radius-md);
  margin-bottom: 10rpx;
  background-color: white;
  transition: background-color 0.2s;
}

/* 使用小程序内置hover属性替代:active伪类 */
.encoding-item-hover {
  background-color: var(--primary-light);
  transition: background-color 0.1s ease;
}

.encoding-item.selected {
  background-color: var(--primary-light);
}

.encoding-name {
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.encoding-confidence {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

.check-icon {
  color: var(--primary-color);
  font-weight: bold;
  margin-left: 10rpx;
}